package com.Hackason.BankAccountProject.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 账户信息响应DTO
 */
@Data
public class AccountInfoResponse {
    
    /**
     * 账户ID
     */
    private String accountId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 昵称
     */
    private String nickname;
    
    /**
     * 卡号
     */
    private String cardId;
    
    /**
     * 余额信息列表（支持多币种）
     */
    private List<BalanceInfo> balances;
    
    /**
     * 交易记录列表
     */
    private List<TransactionRecord> transactions;
    
    /**
     * 余额信息内部类
     */
    @Data
    public static class BalanceInfo {
        /**
         * 余额
         */
        private BigDecimal amount;
        
        /**
         * 币种
         */
        private String currency;
    }
}
