package com.Hackason.BankAccountProject.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易记录DTO
 */
@Data
public class TransactionRecord {
    
    /**
     * 交易ID
     */
    private String transactionId;
    
    /**
     * 交易类型（转入/转出）
     */
    private String transactionType;
    
    /**
     * 对方账户
     */
    private String counterpartyAccount;
    
    /**
     * 交易金额
     */
    private BigDecimal amount;
    
    /**
     * 币种类型
     */
    private String currencyType;
    
    /**
     * 交易状态
     */
    private String status;
    
    /**
     * 交易时间
     */
    private LocalDateTime transactionTime;
    
    /**
     * 交易描述
     */
    private String description;
}
