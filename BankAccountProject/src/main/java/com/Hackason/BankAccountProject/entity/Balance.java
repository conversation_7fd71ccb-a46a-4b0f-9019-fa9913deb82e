package com.Hackason.BankAccountProject.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 余额实体类
 */
@Data
@TableName("balance")
public class Balance {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 账户ID
     */
    private Long accountId;
    
    /**
     * 余额
     */
    private BigDecimal amount;
    
    /**
     * 币种
     */
    private String currency;
}
