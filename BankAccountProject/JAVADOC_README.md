# 银行账户管理系统 JavaDoc 文档

## 📚 文档概述

本项目已为所有主要功能代码文件添加了详细的JavaDoc注释，并通过Maven成功生成了完整的API文档。

## 🎯 已添加JavaDoc注释的文件

### 控制器层 (Controller)
- **AccountController.java** - 银行账户查询控制器
  - 提供RESTful API接口
  - 包含详细的请求/响应示例
  - 完整的异常处理说明

### 服务层 (Service)
- **AccountService.java** - 账户服务接口
- **AccountServiceImpl.java** - 账户服务实现类
  - 详细的业务逻辑说明
  - 完整的方法实现文档

### 数据访问层 (Mapper)
- **UserMapper.java** - 用户数据访问接口
- **BalanceMapper.java** - 余额数据访问接口
- **TransactionStatusMapper.java** - 交易状态数据访问接口
  - 详细的SQL查询说明
  - 参数和返回值文档

### 实体类 (Entity)
- **User.java** - 用户实体类
- **Balance.java** - 余额实体类
- **TransactionStatus.java** - 交易状态实体类
  - 完整的字段说明
  - 数据库映射关系

### 数据传输对象 (DTO)
- **AccountInfoResponse.java** - 账户信息响应DTO
- **TransactionRecord.java** - 交易记录DTO
  - 详细的数据结构说明
  - 使用示例

### 通用类 (Common)
- **Result.java** - 统一响应结果类
- **MyBatisPlusConfig.java** - MyBatis-Plus配置类

## 📖 如何查看JavaDoc文档

### 方法一：直接打开HTML文件
1. 导航到项目目录：`BankAccountProject/target/site/apidocs/`
2. 双击打开 `index.html` 文件
3. 在浏览器中查看完整的API文档

### 方法二：通过Maven命令生成
```bash
# 进入项目目录
cd BankAccountProject

# 生成JavaDoc文档
mvn javadoc:javadoc

# 文档将生成在 target/site/apidocs/ 目录下
```

### 方法三：生成JavaDoc JAR包
```bash
# 生成JavaDoc JAR包
mvn javadoc:jar

# JAR包将生成在 target/ 目录下
```

## 🌟 JavaDoc文档特色

### 1. 中文支持
- 所有注释均为中文，便于理解
- 支持中文搜索和导航

### 2. 详细的类和方法说明
- 每个类都有完整的功能描述
- 每个方法都有详细的参数和返回值说明
- 包含使用示例和注意事项

### 3. 完整的包结构
- 按功能模块分组显示
- 清晰的包依赖关系
- 便于导航和查找

### 4. 丰富的标签使用
- `@param` - 参数说明
- `@return` - 返回值说明
- `@throws` - 异常说明
- `@see` - 相关类引用
- `@since` - 版本信息
- `@author` - 作者信息

## 📁 文档目录结构

```
target/site/apidocs/
├── index.html                 # 主页
├── overview-summary.html      # 概览页面
├── allclasses-index.html      # 所有类索引
├── allpackages-index.html     # 所有包索引
├── com/Hackason/BankAccountProject/
│   ├── controller/            # 控制器文档
│   ├── service/              # 服务层文档
│   ├── mapper/               # 数据访问层文档
│   ├── entity/               # 实体类文档
│   ├── dto/                  # DTO文档
│   ├── common/               # 通用类文档
│   └── config/               # 配置类文档
└── resource-files/           # 样式和脚本文件
```

## 🔍 使用建议

1. **从主页开始**：打开 `index.html` 获得项目整体概览
2. **按模块浏览**：根据功能模块查看相关类的文档
3. **使用搜索功能**：利用内置搜索快速定位特定类或方法
4. **查看类关系**：通过继承树了解类之间的关系

## 📝 注意事项

- 文档基于当前代码状态生成，如有代码更新请重新生成
- 建议在每次重大更新后重新生成JavaDoc文档
- 文档中的示例代码仅供参考，实际使用时请根据具体需求调整

---

**生成时间**：2024年6月8日  
**文档版本**：1.0  
**项目团队**：Hackason Team
